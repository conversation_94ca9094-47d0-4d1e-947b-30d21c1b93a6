packer {
  required_version = ">= 1.9.0"

  required_plugins {
    qemu = {
      version = ">= 1.0.9"
      source  = "github.com/hashicorp/qemu"
    }
  }
}

variable "ssh_username" {
  default = "ubuntu"
}

variable "ssh_password" {
  default = "ubuntu"
}

source "qemu" "ubuntu_x64" {
  qemu_binary       = "/opt/homebrew/bin/qemu-system-x86_64"  # path to QEMU on Mac
  accelerator       = "tcg"                                   # cross-arch emulation

  # Use the cloud image as the base disk instead of ISO
  disk_image        = true
  iso_url           = "/Users/<USER>/Downloads/ubuntu-22.04-server-cloudimg-amd64.img"
  iso_checksum      = "b119a978dcb66194761674c23a860a75cdb7778e95e222b51d7a3386dfe3c920"

  vm_name           = "ubuntu-x64"
  disk_size         = 10240           # 10 GB
  format            = "raw"           # temporary build format
  memory            = 1024            # Increase memory to 1GB

  # Network configuration
  net_device        = "virtio-net"

  # SSH configuration
  ssh_username      = var.ssh_username
  ssh_password      = var.ssh_password
  ssh_wait_timeout  = "20m"
  ssh_timeout       = "20m"

  # Cloud-init configuration
  cd_files = [
    "./user-data",
    "./meta-data"
  ]
  cd_label = "cidata"

  # Boot configuration for cloud image
  boot_wait         = "10s"

  headless          = true
  shutdown_command  = "echo '${var.ssh_password}' | sudo -S shutdown -P now"
}

build {
  sources = ["source.qemu.ubuntu_x64"]

  provisioner "shell" {
    inline = [
      "echo \"FOO is FOO\" > example.txt",
    ]
  }
}

