#cloud-config
# Cloud-init configuration for Ubuntu cloud image

# Set up users
users:
  - name: ubuntu
    sudo: ALL=(ALL) NOPASSWD:ALL
    groups: users, admin, sudo
    home: /home/<USER>
    shell: /bin/bash
    lock_passwd: false
    # Set password to 'ubuntu' (plain text, will be hashed by cloud-init)
    plain_text_passwd: ubuntu

# Enable password authentication for SSH
ssh_pwauth: true

# SSH configuration - ensure SSH daemon allows password auth
ssh:
  emit_keys_to_console: false

# Package updates and installations
package_update: true
package_upgrade: false

# Install essential packages
packages:
  - openssh-server
  - curl
  - wget

# Write SSH config to ensure password authentication is enabled
write_files:
  - path: /etc/ssh/sshd_config.d/99-packer.conf
    content: |
      PasswordAuthentication yes
      PermitRootLogin no
      PubkeyAuthentication yes
      ChallengeResponseAuthentication no
    permissions: '0644'

# Enable services and configure SSH
runcmd:
  - systemctl enable ssh
  - systemctl restart ssh
  - systemctl status ssh
  - echo "SSH service status:" >> /var/log/cloud-init-output.log
  - systemctl is-active ssh >> /var/log/cloud-init-output.log
  - netstat -tlnp | grep :22 >> /var/log/cloud-init-output.log

# Set timezone
timezone: UTC

# Final message
final_message: "Cloud-init setup complete. SSH should be available on port 22."
